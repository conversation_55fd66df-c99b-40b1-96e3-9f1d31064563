import { z } from 'zod'
import { getFeaturedTimeline } from '~/server/utils/backend/mastodon/timeline'

// https://docs.joinmastodon.org/methods/timelines
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { max_id: maxId } = await getValidatedQuery(event, z.object({
    max_id: z.string().optional(),
  }).parse)
  return getFeaturedTimeline(useEnv().DB, domain, person, maxId)
})
