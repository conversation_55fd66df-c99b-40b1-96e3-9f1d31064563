import type { Featured } from '#shared/types'

export function useFeaturedUnreadStatus() {
  const { account } = useAuth()
  const hasUnreadFeatured = ref(false)

  const channel = computed(() =>
    account.value ? `timeline:featured:${toFullHandle(account.value.acct)}` : undefined,
  )

  const { stream } = useStreaming<{ type: string, item: Featured }>(channel)

  const checkUnreadStatus = async () => {
    if (!account.value) {
      hasUnreadFeatured.value = false
      return
    }

    try {
      const response = await $fetch<Featured[]>('/api/timelines/featured', {
        headers: useRequestHeaders(['cookie']),
      })
      hasUnreadFeatured.value = response.some(item => (item.unreadCount ?? 0) > 0)
    }
    catch (error) {
      // Only log error if user is authenticated to avoid noise during SSR
      if (account.value) {
        console.error('Failed to check featured unread status:', error)
      }
      hasUnreadFeatured.value = false
    }
  }

  // Check status on mount and when account changes
  watchEffect(() => {
    if (account.value) {
      checkUnreadStatus()
    }
  })

  watch(stream, (update) => {
    if (update) {
      checkUnreadStatus()
    }
  })

  return {
    hasUnreadFeatured: readonly(hasUnreadFeatured),
    checkUnreadStatus,
  }
}
