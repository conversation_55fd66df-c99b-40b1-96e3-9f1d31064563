<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

const { account } = useAuth()

// Test server-side authentication
const { data: serverAuth, error: serverError } = await useFetch('/api/auth', {
  headers: useRequestHeaders(['cookie']),
}).catch(() => ({ data: ref(null), error: ref('Failed to fetch') }))

// Test the featured timeline endpoint that AccountReadingList uses
const { data: featuredData, error: featuredError } = await useFetch('/api/timelines/featured', {
  headers: useRequestHeaders(['cookie']),
}).catch(() => ({ data: ref(null), error: ref('Failed to fetch featured timeline') }))

const isSSR = !import.meta.client
const processType = import.meta.client ? 'Client' : 'Server'
</script>

<template>
  <div class="test-auth-page">
    <h1>Authentication Test Page</h1>

    <div class="test-section">
      <h2>Environment</h2>
      <p><strong>Is SSR:</strong> {{ isSSR }}</p>
      <p><strong>Process:</strong> {{ processType }}</p>
    </div>

    <div class="test-section">
      <h2>useAuth() State</h2>
      <p><strong>Logged In:</strong> {{ !!account }}</p>
      <p><strong>Account ID:</strong> {{ account?.id || 'None' }}</p>
      <p><strong>Account Username:</strong> {{ account?.username || 'None' }}</p>
    </div>

    <div class="test-section">
      <h2>Direct /api/auth Call</h2>
      <p><strong>Success:</strong> {{ !!serverAuth }}</p>
      <p><strong>Error:</strong> {{ serverError || 'None' }}</p>
      <p><strong>Account ID:</strong> {{ serverAuth?.id || 'None' }}</p>
      <p><strong>Account Username:</strong> {{ serverAuth?.username || 'None' }}</p>
    </div>

    <div class="test-section">
      <h2>Featured Timeline Test</h2>
      <p><strong>Success:</strong> {{ !!featuredData }}</p>
      <p><strong>Error:</strong> {{ featuredError || 'None' }}</p>
      <p><strong>Items Count:</strong> {{ Array.isArray(featuredData) ? featuredData.length : 'N/A' }}</p>
    </div>

    <div class="test-section" v-if="account">
      <h2>AccountReadingList Component Test</h2>
      <AccountReadingList :account="account" />
    </div>
  </div>
</template>

<style scoped>
.test-auth-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section p {
  margin: 0.5rem 0;
}
</style>
